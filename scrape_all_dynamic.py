#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
8591防洗錢審核數據爬取腳本 - 動態檢測版本
自動檢測實際總頁數，使用50個並發
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
import logging
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import re

# 設置日誌
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraping_dynamic.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DynamicScraper:
    def __init__(self, max_workers=50):
        self.base_url = "https://admin.8591.com.tw/admin.php"
        self.max_workers = max_workers
        
        # 基礎headers和cookies
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Host': 'admin.8591.com.tw',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        }
        
        self.base_cookies = {
            '_fbp': 'fb.2.1748415495759.370845561106175856',
            '_tt_enable_cookie': '1',
            '_ttp': '01JWATYFYYB4ZJX9T5RPY34Q0P_.tt.2',
            '_gcl_au': '1.1.640944624.1748415497',
            '_ga_QB2W55G6D7': 'GS2.1.s1748415497$o1$g0$t1748415503$j54$l0$h0',
            '_ga_6F806W7CEQ': 'GS2.1.s1748415497$o1$g0$t1748415503$j54$l0$h0',
            'webp': '1',
            'PHPSESSID': 'cf2ba51fd8d8f293e917d66a919232c23f8e75dd',
            'leftcookie': 'showleft01-showleft11-showleft21-showleft31-showleft41-showleft51-showleft61-showleft71-showleft81-showleft91-showleft101-showleft111-showleft121-showleft131-',
            '_ga_YTJW9Z9SYZ': 'GS2.1.s1753664881$o6$g0$t1753664881$j60$l0$h0',
            '_ga_11R0Y6D4YE': 'GS2.1.s1753664881$o6$g1$t1753664881$j60$l0$h0',
            '_ga_KH53JFXPJE': 'GS2.3.s1753769126$o1$g1$t1753769202$j60$l0$h0',
            '_ga_4Z6B2ZGVVG': 'GS2.1.s1753769126$o1$g1$t1753769261$j60$l0$h0',
            '_ga': 'GA1.1.1526496494.1748415496',
            '_ga_YFKNQX5E65': 'GS2.1.s1753848384$o10$g1$t1753848384$j60$l0$h0',
            '_ga_5G0TRF3C4C': 'GS2.1.s1754028006$o3$g0$t1754028006$j60$l0$h0',
            '__gads': 'ID=b801d2767e685562:T=1748415513:RT=1754028020:S=ALNI_MZV6CkW8BG53KQ58DBpRCLmIyuh1Q',
            '__gpi': 'UID=000010f6dd4ce04a:T=1748415513:RT=1754028020:S=ALNI_MYvx6xsYE8KlsFJQcWVsFlUg4GLgg',
            '__eoi': 'ID=81e74cf7c7cb41bf:T=1748415513:RT=1754028020:S=AA-AfjZRhti2BOkhPNhK6mdOK_PL',
            '_ga_Y1GQYKZVWT': 'GS2.1.s1754028006$o3$g0$t1754028006$j60$l0$h0',
            'ttcsid': '1754028006939::xpxhwTm87XBxoOfdQR4F.3.1754028006939',
            '_clck': '149eul1%7C2%7Cfy3%7C0%7C1974',
            'ttcsid_COUUNB3C77U8DR5JQTDG': '1754028006939::wGW5zMHVy2M7PfJRaGK6.3.1754028007151',
            '_clsk': 'j7y2j0%7C1754028007990%7C1%7C1%7Ce.clarity.ms%2Fcollect'
        }
        
        # 數據存儲 - 使用字典按頁碼存儲，確保順序
        self.page_data = {}
        self.lock = threading.Lock()
        
        # 統計信息
        self.success_count = 0
        self.error_count = 0
        self.total_records = 0
        
    def create_session(self):
        """為每個線程創建獨立的session"""
        session = requests.Session()
        session.headers.update(self.base_headers)
        session.cookies.update(self.base_cookies)
        return session
    
    def detect_total_pages(self):
        """動態檢測實際總頁數"""
        session = self.create_session()
        
        # 先獲取第一頁，檢查分頁信息
        params = {
            'firstRow': 0,
            'totalRows': 30502,  # 先用一個大概的值
            'module': 'laundering',
            'action': 'index',
            'risk': 1,
            'check_status': 1
        }
        
        try:
            response = session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找分頁信息
            pagination = soup.find('div', class_=lambda x: x and 'layui-laypage' in x)
            if pagination:
                text = pagination.get_text()
                
                # 提取總記錄數
                total_match = re.search(r'共\s*(\d+)\s*筆', text)
                if total_match:
                    total_records = int(total_match.group(1))
                    logger.info(f"檢測到總記錄數: {total_records}")
                else:
                    total_records = None
                
                # 提取總頁數
                page_match = re.search(r'第\d+/(\d+)頁', text)
                if page_match:
                    total_pages = int(page_match.group(1))
                    logger.info(f"檢測到總頁數: {total_pages}")
                    return total_pages, total_records
                else:
                    logger.warning("未找到總頁數信息")
            
            # 如果無法從分頁信息獲取，嘗試通過檢查最後一頁
            logger.info("嘗試通過二分查找檢測實際總頁數...")
            return self.binary_search_last_page(session), None
            
        except Exception as e:
            logger.error(f"檢測總頁數失敗: {e}")
            return None, None
    
    def binary_search_last_page(self, session):
        """二分查找最後一頁"""
        left, right = 1, 1000  # 假設最多1000頁
        last_valid_page = 1
        
        while left <= right:
            mid = (left + right) // 2
            first_row = (mid - 1) * 50
            
            params = {
                'firstRow': first_row,
                'totalRows': 50000,  # 用一個大值
                'module': 'laundering',
                'action': 'index',
                'risk': 1,
                'check_status': 1
            }
            
            try:
                response = session.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                soup = BeautifulSoup(response.text, 'html.parser')
                table = soup.find('table', class_=lambda x: x and 'layui-table' in x)
                
                if table:
                    tbody = table.find('tbody')
                    if tbody:
                        rows = tbody.find_all('tr')
                        if len(rows) > 0:
                            last_valid_page = mid
                            left = mid + 1
                            logger.info(f"第 {mid} 頁有數據，繼續向右查找")
                        else:
                            right = mid - 1
                            logger.info(f"第 {mid} 頁無數據，向左查找")
                    else:
                        right = mid - 1
                else:
                    right = mid - 1
                    
                time.sleep(0.1)  # 避免請求過快
                
            except Exception as e:
                logger.warning(f"檢查第 {mid} 頁時出錯: {e}")
                right = mid - 1
        
        logger.info(f"二分查找完成，最後有效頁數: {last_valid_page}")
        return last_valid_page
    
    def get_page_data(self, page_num, first_row):
        """獲取單頁數據的線程函數"""
        session = self.create_session()
        
        params = {
            'firstRow': first_row,
            'totalRows': 30502,
            'module': 'laundering',
            'action': 'index',
            'risk': 1,
            'check_status': 1
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 隨機延遲，避免請求過於集中
                time.sleep(random.uniform(0.1, 0.5))
                
                response = session.get(self.base_url, params=params, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                # 解析數據
                page_data = self.parse_page_data(response.text, page_num)
                
                # 線程安全地存儲數據
                with self.lock:
                    self.page_data[page_num] = page_data
                    self.success_count += 1
                    self.total_records += len(page_data)
                    
                    if self.success_count % 10 == 0:
                        logger.info(f"已完成 {self.success_count} 頁，總記錄數: {self.total_records}")
                
                logger.info(f"第 {page_num} 頁完成，獲取 {len(page_data)} 條記錄")
                return page_num, page_data
                
            except Exception as e:
                logger.warning(f"第 {page_num} 頁第 {attempt + 1} 次嘗試失敗: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(1, 3))
                else:
                    with self.lock:
                        self.error_count += 1
                    logger.error(f"第 {page_num} 頁最終失敗")
                    return page_num, []
        
        return page_num, []
    
    def parse_page_data(self, html_content, page_num):
        """解析單頁HTML內容"""
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找包含layui-table的table元素
        table = soup.find('table', class_=lambda x: x and 'layui-table' in x)
        
        if not table:
            logger.warning(f"第 {page_num} 頁未找到數據表格")
            return []
        
        tbody = table.find('tbody')
        if not tbody:
            logger.warning(f"第 {page_num} 頁未找到表格主體")
            return []
        
        rows = tbody.find_all('tr')
        page_data = []
        
        for i, row in enumerate(rows):
            try:
                cells = row.find_all('td')
                if len(cells) < 10:  # 確保有足夠的列
                    continue
                
                # 提取會員編號（真實值）
                member_id_cell = cells[1]
                member_id_link = member_id_cell.find('a', class_='hide-val')
                member_id = member_id_link.get('real', '') if member_id_link else ''
                
                # 提取送審姓名（真實值）
                name_cell = cells[2]
                name_span = name_cell.find('span', class_='hide-val')
                name = name_span.get('real', '') if name_span else ''
                
                # 提取送審開始時間
                start_time = cells[3].get_text(strip=True)
                
                # 提取審核完成時間
                end_time = cells[4].get_text(strip=True)
                
                # 提取審核結果
                result = cells[5].get_text(strip=True)
                
                # 提取部門主管信息
                dept_manager = self.extract_manager_info(cells[6])
                
                # 提取高階管理人員信息
                senior_manager = self.extract_manager_info(cells[7])
                
                # 提取會員資金來源
                fund_source = cells[8].get_text(strip=True)
                
                # 提取操作列信息（第10列）
                operation = cells[9].get_text(strip=True)
                
                row_data = {
                    '會員編號': member_id,
                    '送審姓名': name,
                    '送審開始時間': start_time,
                    '審核完成時間': end_time,
                    '審核結果': result,
                    '部門主管': dept_manager,
                    '高階管理人員': senior_manager,
                    '會員資金來源': fund_source,
                    '操作': operation,
                    '_page_num': page_num,  # 添加頁碼用於排序
                    '_row_num': i + 1       # 添加行號用於排序
                }
                
                page_data.append(row_data)
                
            except Exception as e:
                logger.warning(f"解析第 {page_num} 頁第 {i+1} 行數據時出錯: {e}")
                continue
        
        return page_data
    
    def extract_manager_info(self, cell):
        """提取主管審核信息"""
        text = cell.get_text(strip=True)
        if '已通過' in text:
            # 提取姓名和時間
            lines = text.split('\n')
            clean_lines = [line.strip() for line in lines if line.strip()]
            if len(clean_lines) >= 2:
                return f"{clean_lines[0]} {clean_lines[1]}"
            return text
        elif '通過' in text or '拒絕' in text:
            return "待審核"
        else:
            return text
    
    def scrape_all_data(self):
        """使用多線程爬取所有數據"""
        # 首先檢測實際總頁數
        total_pages, total_records_hint = self.detect_total_pages()
        
        if not total_pages:
            logger.error("無法檢測總頁數，使用默認值47頁")
            total_pages = 47
        
        logger.info(f"開始多線程爬取，總共 {total_pages} 頁，使用 {self.max_workers} 個線程")
        
        # 創建任務列表
        tasks = []
        for page in range(total_pages):
            page_num = page + 1
            first_row = page * 50
            tasks.append((page_num, first_row))
        
        # 使用線程池執行任務
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任務
            future_to_page = {
                executor.submit(self.get_page_data, page_num, first_row): page_num 
                for page_num, first_row in tasks
            }
            
            # 等待所有任務完成
            for future in as_completed(future_to_page):
                page_num = future_to_page[future]
                try:
                    result = future.result()
                except Exception as e:
                    logger.error(f"線程執行第 {page_num} 頁時出錯: {e}")
        
        logger.info(f"爬取完成！成功: {self.success_count} 頁，失敗: {self.error_count} 頁，總記錄數: {self.total_records}")
    
    def save_to_excel(self, filename='result_dynamic.xlsx'):
        """按頁面順序保存數據到Excel文件"""
        if not self.page_data:
            logger.warning("沒有數據可保存")
            return
        
        logger.info("開始整理和排序數據...")
        
        # 按頁碼順序整理數據
        all_data = []
        for page_num in sorted(self.page_data.keys()):
            page_records = self.page_data[page_num]
            # 按行號排序
            page_records.sort(key=lambda x: x.get('_row_num', 0))
            all_data.extend(page_records)
        
        # 移除輔助字段
        for record in all_data:
            record.pop('_page_num', None)
            record.pop('_row_num', None)
        
        df = pd.DataFrame(all_data)
        
        # 確保列順序正確
        columns_order = [
            '會員編號', '送審姓名', '送審開始時間', '審核完成時間', 
            '審核結果', '部門主管', '高階管理人員', '會員資金來源', '操作'
        ]
        
        df = df.reindex(columns=columns_order)
        
        # 保存到Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        logger.info(f"數據已保存到 {filename}，共 {len(df)} 條記錄")
        
        # 顯示統計信息
        print(f"\n=== 爬取統計 ===")
        print(f"成功頁數: {self.success_count}")
        print(f"失敗頁數: {self.error_count}")
        print(f"總記錄數: {len(df)}")
        print(f"數據文件: {filename}")

def main():
    """主函數"""
    scraper = DynamicScraper(max_workers=50)
    
    try:
        start_time = time.time()
        
        # 爬取所有數據
        scraper.scrape_all_data()
        
        # 保存到Excel
        scraper.save_to_excel('result_dynamic.xlsx')
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        logger.info(f"任務完成！總耗時: {elapsed_time:.2f} 秒")
        
    except Exception as e:
        logger.error(f"程序執行出錯: {e}")

if __name__ == "__main__":
    main()
