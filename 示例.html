<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
    <head>
        <title>8591寶物交易網</title>
        <meta http-equiv="Content-Type" content="text/html; charset=" UTF-8 ">
    <link href=" //static.8591.com.tw/css/admin.css " rel=" stylesheet " type=" text/css ">
    <link href=" //static.8591.com.tw/javascript/layui/2.5.5/css/layui.css " rel=" stylesheet " type=" text/css ">
    <!-- bt样式配置 -->
<meta name=" renderer " content=" webkit ">
<link href=" //statics.8591.com.tw/css/admin/opendiv.css " rel=" stylesheet " type=" text/css ">
<link href=" //statics.8591.com.tw/css/admin/bt.css " rel=" stylesheet " type=" text/css ">
<link href=" //statics.8591.com.tw/css/admin/j-reset.css " rel=" stylesheet " type=" text/css ">
<link href=" //statics.8591.com.tw/css/admin/admin-new.css " rel=" stylesheet " type=" text/css ">
<!--[if lt IE 9]>
  <script src=" //cdn.bootcss.com/html5shiv/3.7.2/html5shiv.min.js "></script>
  <script src=" //cdn.bootcss.com/respond.js/1.4.2/respond.min.js "></script>
<style>
.form-control {display: inline-block; width: auto;}
</style>  
<![endif]-->
<style type=" text/css ">
#head{ height: 48px; margin-bottom: 20px;}
#head a {margin-right: 10px; }
</style>    <script type=" text/javascript " src=" //static.8591.com.tw/javascript/selectAllAdmin.js "></script>
    <link rel=" stylesheet " href=" //static.8591.com.tw/css/nestable.css ">
    <style>
        .func-nav {
            width: 100%;
        }

        .layui-fluid {
            padding: 0;
        }

        .layui-form-label {
            width: auto
        }

        .layui-icon {
            margin-left: 4px;
        }

        .layui-anim layui-anim-upbit {
            z-index: 99999;
        }

        .quick-add .layui-form-mid {
            padding: 4px 0 !important;
        }

        .layui-table[lay-size=sm] td,
        .layui-table[lay-size=sm] th {
            padding: 3px 3px;
            text-align: center;
        }

        table .layui-table-cell {
            height: auto;
            overflow: visible;
            text-overflow: inherit;
            white-space: normal;
        }

        .layui-laypage .pageNum:link,
        .layui-laypage .pageNum:visited {
            display: inline-block;
            *display: inline;
            *zoom: 1;
            vertical-align: middle;
            padding: 0 15px;
            height: 28px;
            line-height: 28px;
            margin: 0 -1px 5px 0;
            background-color: #fff;
            color: #333;
            font-size: 12px;
            border: 1px solid #e2e2e2;
            font-weight: 400;
        }

        .layui-laypage img {
            margin-top: 5px;
        }

        .layui-laypage .pageCurrentfang {
            background-color: #009688;
            color: #fff;
            font-weight: 400;
        }
    </style>
</head>

<body leftmargin=" 0 " topmargin=" 0 " marginwidth=" 0 " marginheight=" 0 ">
<link href=" //statics.8591.com.tw/css/admin/opendiv.css " rel=" stylesheet " type=" text/css ">
<script language=" javascript " src=" https://code.jquery.com/jquery-3.3.1.min.js "></script>
<script src=" https://code.jquery.com/jquery-migrate-1.2.1.js "></script>
<script type=" text/javascript " src=" //statics.8591.com.tw/js/admin/jquery.cookie.js "></script>
<script type=" text/javascript " src=" //s.8591.com.tw/js/seajs/dist/sea.js "></script>
<script>
  var protocol = window.location.protocol;
  seajs.config({
    alias:{
      popbox: protocol+'//s.8591.com.tw/js/ued/admin/popbox/popbox.js',
      popbox2: protocol+'//s.8591.com.tw/js/ued/admin/popbox/popbox-2.0.js',
      popboxCss: protocol+'//s.8591.com.tw/js/ued/admin/popbox/popbox.css',
      tooltips: protocol+'//s.8591.com.tw/js/ued/admin/tooltips/tooltips.js',
      jqueryPopbox: protocol+'//s.8591.com.tw/js/ued/admin/popbox/popbox.jquery.js',

      comUpload: protocol+'//s.8591.com.tw/js/src/plugin/com-upload.js'
    },
    charset:'utf-8'
  });
</script><script type=" text/javascript " src=" //static.8591.com.tw/javascript/adup.js "></script>

<script>seajs.use('jqueryPopbox',function(){});</script>
<div class=" func-nav ">
  <span class=" float-rt ">
    <div class=" float-rt admin-nav ">
      <!-- <i class=" admin-nav-ico "></i> -->
      <div class=" admin-nav-ico ">
        <img src=" https://oa.addcn.com/Public/Static/userphoto/00175.jpg " onerror=" this.remove()">
      </div>
      <span class=" admin-nav-list ">
                <a href=" /admin.php?module=profile&action= edit ">修改资料</a>
        <a href=" /admin.php?module=profile&action= changePwd ">修改密码</a>
        <a href=" http://www.ip138.com " target=" _blank ">IP查詢</a>
        <a href=" http://www.8591.com.tw " target=" _blank ">网站前臺</a>
        <a href=" /admin.php?module=main&action= main ">後臺首頁</a>
        <a href=" https://yyadmin.8591.com.tw/report " target=" _blank ">數據報表</a>
                <!--<a href=" https://wiki.8591.com.tw /" target=" _blank ">公司知識庫</a>-->
                <!--<a href=" http://bbs.addcn.com/forum.php?mod=viewthread&tid= 221&page=1&extra= #pid547 " target=" _blank ">後臺討論區</a>-->
                <a href=" http://bbs.8591.com.tw " target=" _blank ">前臺討論區</a>
        <!--<a href=" http://bbs.addcn.com/forum.php?mod=forumdisplay&fid= 104 " target=" _blank ">公司BBS</a>-->
        <a href=" /admin.php?action=loginOut ">登出系统</a>
      </span>
    </div>
        <!-- 客服主管 --> 黃興興 <!-- 歡迎回來 ^_^  -->
  </span>
  <ul class=" apart-nav ">
    <li class=" func-nav-on ">
        <a href=" /admin.php?module=main&action= main&groupType=customer_service ">客服</a>
    </li>
    <li >
                    <a href=" /admin.php?module=main&action= main&groupType=service ">營運</a>
            </li>
    <li >
            </li>
  </ul>
  <div class=" pos-re noticeTop ">
      <ul id=" notice-slider ">
                </ul>
    </div>
        <!--
      <a href='./admin.php?module=main&action=main&version=new' style='color:red;'>新版後臺</a>
      -->
        <input type=" checkbox " id=" keeplive-checkbox "  value="">
    <label for=" keeplive-checkbox ">保持在线</label>
  </div>
</div>

<div id=" leftOverDiv "></div>

<div id=" leftdivJUMP ">
  <h5 class=" boxh5 "><span>用户自定义列表</span><input type=" button " value=" 关闭 " id=" leftCloseDiv "></h5>
  <FORM METHOD=POST ACTION="" onsubmit=" return addself();"> 
  <ul>
  <li>名称：<INPUT TYPE=" text " NAME=" leftname " id=" leftname " style=" width:200px;height:22px;"></li>
  <li>网址：<INPUT TYPE=" text " NAME=" lefturl " id=" lefturl " style=" width:200px;height:22px;"></li>
  <li><INPUT TYPE=" submit " value=" 确定提交 "></li>
  </ul>  
  </FORM>
  <ul>    
  <li style=" max-height: 656px;overflow-y: auto;">
    <table width=" 100%" border=" 0 " cellpadding=" 1 " cellspacing=" 1 " class=" boxtable ">
      <tr>
      <th>顺序</th>
      <th>名称</th>
      <th>网址</th>  
      <th width=" 50px ">操作</th>   
    </tr>
    <tbody id=" showlist ">   
      </tbody>
    <tr>
      <td colspan=" 4 " style=" text-align:left;"><INPUT TYPE=" button " id=" leftpostall " value=" 批量修改 " style=" width:60px;height:22px;"></td>   
    </tr>
    </table>   
  </li> 
  </ul> 
</div><script src=" //static.8591.com.tw/javascript/layui/2.5.5/layui.js "></script>
<script src=" //static.8591.com.tw/javascript/nestable.js "></script>
<script src=" //static.8591.com.tw/javascript/jquery.serializejson.min.js "></script>
<div class=" clear "></div>
<div class=" j-container center-block clearfix ma-top-10 j-container-new ">
    <div class=" j-c-menu ">
        
<script type=" text/javascript ">
//jQuery.noConflict();
var delinfo = function(id)
{
    var data = {};
	data['module'] = 'adminself';
	data['action'] = 'delInfo';
	data['id']     = id;
	jQuery.post(" /admin.php?"+new Date().getTime(),data,function (json){
		getinfo();
		alert(json['err']);
	},'json');
}
var addself = function()
{
	var data = {};
    data['name']   = jQuery('#leftname').val(); 
	data['url']    = jQuery('#lefturl').val(); 
	data['module'] = 'adminself';
	data['action'] = 'addSelf';
	if(data['name']=='')
	{
	    alert('请输入名称！');
		return false;
	}
	if(data['url']=='')
	{
	    alert('请输入网址！');
		return false;
	}
	jQuery.post(" /admin.php?"+new Date().getTime(),data,function (json){
		jQuery('#name').val(''); 
		jQuery('#url').val(''); 
		getinfo();
	    alert(json['err']);
	},'json');
    return false; 
}
var getinfo = function()
{
	var data = {};
    data['module'] = 'adminself';
	data['action'] = 'getInfo';	
	jQuery.post(" /admin.php?"+new Date().getTime(),data,function (json){
		var html = '';
		var myhtml = ''
		jQuery.each(json,function(i,v){
		    html += gethtml(v['bys'],v['name'],v['url'],v['id']);
			myhtml += getmylove(v['name'],v['url']);
		});
        jQuery(" #showlist ").html(html);
		jQuery(" #showmylove ").html(myhtml);
	},'json')
}

var gethtml = function(bys,name,url,id)
{
    return '<tr><td><INPUT TYPE=" text " NAME=" bysvalue " style=" width:40px;height:22px;" value="'+bys+'"></td><td><INPUT TYPE=" text " NAME=" namevalue " style=" width:100px;height:22px;" value="'+name+'"></td><td><INPUT TYPE=" text " NAME=" urlvalue " style=" width:460px;height:22px;" value="'+url+'"><INPUT TYPE=" hidden " NAME=" idvalue " value="'+id+'"></td><td><a href=" javascript:" onclick=" delinfo('+id+' );">删除</a></td></tr>';
}
var getmylove = function(name,url)
{
    return '<a href="'+url+'">'+name+'</a>';
}

var leftgetcookie = function()
{
	var leftcookie = jQuery.cookie('leftcookie');
	if(leftcookie == null)
	{
	    var num = jQuery(" #hiddenlefti ").val();
		var str = '';
		for(var i=0;i<parseInt(num);i++)
		{
		    str += 'showleft'+i+'1-';
		}		
		jQuery.cookie('leftcookie',str,{expires: 30});
	}else
	{
		
	    var arr = leftcookie.split('-');
		jQuery.each(arr,function(i,v){
		    if(v=='showleft'+i+'1')
			{
			    jQuery(" #showleft "+i).css({display:" block "});
			}else
			{
			    jQuery(" #showleft "+i).css({display:" none "});
			}
		});
	}
}
var leftshow = function(i)
{
	var leftcookie = jQuery.cookie('leftcookie');
	if(leftcookie == null)
	{
	    leftgetcookie();
		leftcookie = jQuery.cookie('leftcookie');
	}
    var test = " showleft "+i+" 1 ";
    var text = " showleft "+i+" 2 ";
	var newleftcookie = '';
	if(leftcookie.indexOf(test) >=0)
	{
	    newleftcookie = leftcookie.replace(test,text);
	}else
	{
	    newleftcookie = leftcookie.replace(text,test);
	}
	if(newleftcookie!='')
	{
	    jQuery.cookie('leftcookie',null);
	    jQuery.cookie('leftcookie',newleftcookie,{expires: 30});
	}	
    jQuery(" #showleft "+i).toggle();
}

getinfo();
</script>
<div id=" leftMenu " style=" font-family:Verdana, Arial,宋体;">
<ul><strong><a href=" javascript:" onclick=" leftshow(10000);">我的最愛</a><input type=" button " value=" 設置 " id=" leftOpenDiv "></a></strong>
<div id='showleft10000'>
<li><span id=" showmylove "></span></li>
<div class='clear'></div>
</div>
</ul>
<div class='clear'></div>
<ul><strong onclick='leftshow(0);'>會員管理</strong><div id='showleft0'><li><a href='/admin.php?module=userInfo&action=index'>會員列表</a><a href='/admin.php?module=userInfo&action=userSet'>關注會員</a><a href='/admin.php?module=userInfo&action=confirmList'>重要資料修改</a><a href='/admin.php?module=wrap_up&action=mobileList'>簡訊管理</a><a href='/admin.php?module=userInfo&action=delInfoList'>個資管理</a><a href='/admin.php?module=userInfo&action=esunAccount'>玉山A類帳戶</a><a href='/admin.php?module=dealer_money&action=index'>經銷商列表</a><a href='/admin.php?module=delRecord&action=list'>個資刪除</a><a href='/admin.php?module=userRelation&action=list'>代儲賣家管理</a><a href='/admin.php?module=line&action=index'>LINE 帳號</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(1);'>電話管理</strong><div id='showleft1'><li><a href='/admin.php?module=newIvr&action=callTime'>來電列表</a><a href='/admin.php?module=serviceTelNew&action=index'>記錄列表</a><a href='/admin.php?module=newIvr&action=telCheck'>電話驗證</a><a href='/admin.php?module=ivr&action=loginCheck'>電話解鎖</a><a href='/admin.php?module=ivr&action=faxList'>傳真列表</a><a href='/admin.php?module=ivr&action=extList'>轉接號</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(2);'>客服事務管理</strong><div id='showleft2'><li><a href='/admin.php?module=guestBook&action=list'>留言列表</a><a href='/admin.php?module=appeal&action=list'>申訴列表</a><a href='/admin.php?module=message&action=list'>公告管理</a><a href='/admin.php?module=service&action=index'><b>客服事務</b></a><a href='/admin.php?module=udesk&action=customer'>在線客服</a><a href='/admin.php?module=udesk&action=index'>在線記錄列表</a><a href='/admin.php?module=laundering&action=index&risk=1&check_status=1'>防洗錢審核</a><div class='clear'></div></li><li><a href='/admin.php?module=prop&action=index' style='width:170px;'>優惠結算活動審核</a></li><div class='clear'></div><li><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(3);'>資料審核</strong><div id='showleft3'><li><a href='/admin.php?module=userBank&action=takeMoneyChangeList'>修改提款资料</a><a href='/admin.php?module=userSet&action=identifyIdcardWaitHandle&type=0'>實名驗證</a><a href='/admin.php?module=appeal&action=forgetPhone'>修改註冊手機</a><a href='/admin.php?module=im&action=historyMessages'>聊一聊</a><a href='/admin.php?module=im&action=onlineServiceStat'>客服聊一聊</a><a href='/admin.php?module=mallList&action=examine'>待審核</a><a href='/admin.php?module=dealer_msg&action=activeList&status=ZF'>經銷商待審核</a><a href='/admin.php?module=userInvoice&action=list'>發票審核</a><a href='/admin.php?module=userDeal&action=abroadCardUserList'>海外購卡申請</a><a href='/admin.php?module=activeStopPrize&action=prizeRecord'>中獎信息</a><a href='/admin.php?module=userInfo&action=autoCancelPhone'>自助註銷手機</a><a href='/admin.php?module=rename&action=list&status=2'>自助修改姓名</a><div class='clear'></div></li><li><a href='/admin.php?module=userCancle&action=accountFocus' style='width:170px;'>帳號重複出售關注</a></li><div class='clear'></div><li><div class='clear'></div></li><li><a href='/admin.php?module=storage&action=applyList' style='width:170px;'>代儲賣家權限申請</a></li><div class='clear'></div><li><div class='clear'></div></li><li><a href='/admin.php?module=repeatSold&action=list&status=0' style='width:170px;'>賬號風險訂單關注</a></li><div class='clear'></div><li><a href='/admin.php?module=editName&action=index&status=1'>姓名審核</a><a href='/admin.php?module=kol&action=index'>KOL審核</a><a href='/admin.php?module=kol&action=withdrawApply&state=0'>返利申請</a><a href='/admin.php?module=idcard&action=changeLog'>修改公司統編</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(4);'>交易管理</strong><div id='showleft4'><li><a href='/admin.php?module=mallList&action=list'>交易狀態</a><a href='/admin.php?module=serviceRecord&action=list'>交易糾紛</a><a href='/admin.php?module=order&action=index'>訂單管理</a><a href='/admin.php?module=hkOrder&action=index'>香港訂單管理</a><a href='/admin.php?module=userCancle&action=accMsgList'>帳號交易管理</a><div class='clear'></div></li><li><a href='/admin.php?module=userCancle&action=sellUserCount' style='width:170px;'>手遊頁遊月交易</a></li><div class='clear'></div><li><a href='/admin.php?module=checkRule&action=serviceDelList'>刪除商品</a><a href='/admin.php?module=userInfo&action=mallShowLimit'>修改商品顯示</a><a href='/admin.php?module=wareDeal&action=refundedList'>自助退款列表</a><a href='/admin.php?module=storage&action=sellUser'>代儲賣家管理</a><a href='/admin.php?module=account&action=seller'>賬號賣家管理</a><a href='/admin.php?module=udesk&action=aiReceiveList'>AI外呼列表</a><a href='/admin.php?module=account&action=agreement'>切結書列表</a><a href='/admin.php?module=illegalList&action=list'>黑名單風控</a><a href='/admin.php?module=insureCompensation&action=list'>平台服務列表</a><a href='/admin.php?module=kol&action=incomeRecord'>KOL 返利</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(5);'>T點管理</strong><div id='showleft5'><li><a href='/admin.php?module=userMemory&action=list'>銀行付款</a><a href='/admin.php?module=shopPay&action=advancedList'>超商付款</a><div class='clear'></div></li><li><a href='/admin.php?module=aclink&action=payList' style='width:170px;'>連接銀行帳戶付款</a></li><div class='clear'></div><li><a href='/admin.php?module=wareDeal&action=list&type=t'>T點轉帳</a><a href='/admin.php?module=userBank&action=advance'>高級提款</a><a href='/admin.php?module=userMemory&action=denyMoney'>T點圈存</a><a href='/admin.php?module=userMemory&action=withinTransfer'>內部轉帳</a><a href='/admin.php?module=userSafeBank&action=list'>保證金列表</a><a href='/admin.php?module=userSafeBank&action=refundMoney'>退保申請列表</a><a href='/admin.php?module=saveBank&action=list'>銀行管理</a><a href='/admin.php?module=payment&action=risk'>風控證件審核</a><a href='/admin.php?module=recognition&action=index'>人證核驗</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(6);'>競價推薦</strong><div id='showleft6'><li><a href='/admin.php?module=buyAd&action=newRecommend'>競價推薦</a><a href='/admin.php?module=buyAd&action=newRecBankList'>信用競價推薦</a><a href='/admin.php?module=buyAd&action=keywordsRecommend'>關鍵詞廣告</a><div class='clear'></div></li><li><a href='/admin.php?module=buyAd&action=kwUseList' style='width:170px;'>每日關鍵詞管理</a></li><div class='clear'></div><li><a href='/admin.php?module=buyAd&action=buyAdMoney'>用戶推薦餘額</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(7);'>遊戲管理</strong><div id='showleft7'><li><a href='/admin.php?module=gameList&action=addForm'>新增遊戲</a><a href='/admin.php?module=gameList&action=list'>遊戲列表</a><a href='/admin.php?module=mobileArticle&action=list'>手遊文章管理</a><a href='/admin.php?module=gameList&action=ecological'>遊戲生態表</a><a href='/admin.php?module=gameList&action=gameList'>遊戲進階設定</a><a href='/admin.php?module=recharge&action=list'>代儲標品化</a><a href='/admin.php?module=gameLib&action=index'>遊戲庫管理</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(8);'>系統設置</strong><div id='showleft8'><li><a href='/admin.php?module=system&action=list'>系统设置列表</a><a href='/admin.php?module=popedom&action=userList'>管理员列表</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(9);'>日誌</strong><div id='showleft9'><li><a href='/admin.php?module=log&action=adminLogin'>管理員登入</a><div class='clear'></div></li><li><a href='/admin.php?module=log&action=scriptExec&typeTag=AdminExec' style='width:170px;'>管理程式執行日志</a></li><div class='clear'></div><li><div class='clear'></div></li><li><a href='/admin.php?module=log&action=scriptExec&typeTag=AutoExec' style='width:170px;'>自動程式執行日志</a></li><div class='clear'></div><li><div class='clear'></div></li><li><a href='/admin.php?module=log&action=otherLog&typeTag=moneyZero' style='width:170px;'>會員T點異常日志</a></li><div class='clear'></div><li><div class='clear'></div></li><li><a href='/admin.php?module=log&action=scriptExec&typeTag=SmsExec' style='width:170px;'>簡訊回傳程式執行日志</a></li><div class='clear'></div><li><a href='/admin.php?module=log&action=regAccount'>注册账号日志</a><div class='clear'></div></li><li><a href='/admin.php?module=log&action=adminOperation' style='width:170px;'>管理員操作日志</a></li><div class='clear'></div><li><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(10);'>其他</strong><div id='showleft10'><li><a href='/admin.php?module=ppos&action=ppos'>會員建議</a><a href='/admin.php?module=poiceDocument&action=poiceDocList'>警察公文檔案</a><div class='clear'></div></li><li><a href='/admin.php?module=hiddenBank&action=index' style='width:170px;'>隱藏會員商品T點記錄</a></li><div class='clear'></div><li><a href='/admin.php?module=hiddenBank&action=openIndex'>開通登陸</a><a href='/admin.php?module=document&action=userDenyNote'>停權原因編輯</a><div class='clear'></div></li><li><a href='/admin.php?module=system&action=phishingList' style='width:170px;'>釣魚網站預設列表</a></li><div class='clear'></div><li><div class='clear'></div></li><li><a href='/admin.php?module=tools&action=fixHitCount' style='width:170px;'>賣場瀏覽量修正</a></li><div class='clear'></div><li><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(11);'>客服質檢</strong><div id='showleft11'><li><a href='/admin.php?module=qc&action=index'>質檢數據總表</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(12);'>風控</strong><div id='showleft12'><li><a href='/admin.php?module=rms&action=exceptionMonitoringList'>异常监控</a><a href='/admin.php?module=rms&action=tableMonitoringList'>数据监控</a><a href='/admin.php?module=rmsRule&action=rules'>規則表</a><a href='/admin.php?module=safeSetting&action=index'>安全係數</a><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><ul><strong onclick='leftshow(13);'>智齒自助功能數據</strong><div id='showleft13'><li><div class='clear'></div></li><li><a href='/admin.php?module=sobot&action=receivingRecord' style='width:170px;'>查詢領收進度數據</a></li><div class='clear'></div><li><div class='clear'></div></li><div class='clear'></div></div></ul><div class='clear'></div><input type=" hidden " id=" hiddenlefti " value=" 14 "><div class='clear'></div>
</div>
<script type=" text/javascript ">
    jQuery(" #leftOpenDiv ").click(function(){
        var width  = ((jQuery(document).width()/2)-(700/2))+" px ";
		jQuery(" #leftOverDiv ").css({ display:" block ", height:jQuery(document).height() ,width:jQuery(document).width()});
		jQuery(" #leftdivJUMP ").css({display:" block ",left:width,top:" 60px "})

	})
	jQuery(" #leftCloseDiv ").click(function(){
		jQuery(" #leftOverDiv ").css({ display: " none "});
		jQuery(" #leftdivJUMP ").toggle();
	})
	jQuery(" #leftpostall ").click(function(){
		var data = {}, bys = {}, name = {}, url = {}, id = {};
		data['module'] = 'adminself';
	    data['action'] = 'postAll';	
	    jQuery(" input[name=bysvalue]").each(function(i,v){
		    bys[i] = jQuery(v).val();
		});
		jQuery(" input[name=namevalue]").each(function(i,v){
		    name[i] =jQuery(v).val();
		});
		jQuery(" input[name=urlvalue]").each(function(i,v){
		    url[i] = jQuery(v).val();
		});
		jQuery(" input[name=idvalue]").each(function(i,v){
		    id[i] = jQuery(v).val();
		});
		data['bys']  = bys;
		data['name'] = name;
		data['url']  = url;
		data['id']   = id;
		jQuery.post(" /admin.php?"+new Date().getTime(),data,function (json){	
			getinfo();
	        alert(json['err']);
	    },'json')
	})

leftgetcookie();
</script>    </div>
    <div class=" j-c-cnt ">
        <div class=" well-sm text-left layout-main ">
            <div class=" layui-content ">
                <div class=" layui-fluid ">
                    <div class=" layui-row ">
                        <div class=" layui-col-md12 ">
                            <div class=" layui-card " style=" margin-bottom:0 ">
                                <div class=" layui-card-header ">
                                    反洗钱法审核列表
                                </div>
                                <div class=" layui-card-body ">
                                    <div class=" layui-card layui-tab layui-tab-brief ">
                                        <ul class=" layui-tab-title ">
                                            <li class=" menu-type "
                                                data-option=" 0 ">
                                                <a href=" ./admin.php?module=laundering&action= index ">審核列表</a>
                                            </li>
                                            <li class=" menu-type "
                                                data-option=" 1 ">
                                                <a href=" ./admin.php?module=laundering&action= largeOrders ">單筆5萬訂單</a>
                                            </li>
                                            <li class=" menu-type layui-this " data-option=" 2 ">
                                                <a href=" ./admin.php?module=laundering&action= index&risk=1&check_status= 1 ">高風險名單</a>
                                            </li>
                                            <li class=" menu-type " data-option=" 3 ">
                                                <a href=" ./admin.php?module=laundering&action= restriction ">告誡名單</a>
                                            <li class=" menu-type " data-option=" 4 ">
                                                <a href=" ./admin.php?module=laundering&action= realNameReview ">實名資料審核</a>
                                            </li>
                                        </ul>
                                    </div>
                                                                        <div class=" layui-card-body ">
                                        <div class=" layui-card layui-tab layui-tab-brief ">
                                            <ul class=" layui-tab-title ">
                                                <li class=" menu-type layui-this " data-option=" 1 ">
                                                    <a href=" ./admin.php?module=laundering&action= index&risk=1&check_status= 1 ">待復核</a>
                                                </li>
                                                <li class=" menu-type " data-option=" 1 ">
                                                    <a href=" ./admin.php?module=laundering&action= index&risk=1&check_status= 2 ">已復核</a>
                                                </li>
                                                <li class=" menu-type " data-option="">
                                                    <a href=" ./admin.php?module=laundering&action= index&risk=1 ">全部</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                                                        <form class=" layui-form " id=" search-form " action=" ?" method=" get ">
                                        <input type=" hidden " name=" type " value=" 0 ">
                                        <br>
                                        <div class=" layui-inline ">
                                            <div class=" layui-input-inline ">
                                                <input autocomplete=" off " type=" text " class=" layui-input " id=" startDate " name=" startDate " placeholder=" 起始日期 ">
                                            </div>
                                            <div class=" layui-input-inline ">-</div>
                                            <div class=" layui-input-inline ">
                                                <input autocomplete=" off " type=" text " class=" layui-input " id=" endDate " name=" endDate " placeholder=" 结束日期 ">
                                            </div>
                                        </div>
                                        <div class=" layui-inline ">
                                            <div class=" layui-input-inline " style=" width: 140px;">
                                                <input class=" layui-input " name=" user_id " id=" user_id " placeholder=" 用户ID " value="">
                                            </div>
                                            <div class=" layui-input-inline " style=" width: 140px;">
                                                <input class=" layui-input " name=" name " id=" name " placeholder=" 送審姓名 " value="">
                                            </div>
                                            <div class=" layui-inline ">
                                                <div class=" layui-input-inline " style=" width: 150px;">
                                                    <select name=" check_result " id=" check_result " lay-filter=" check_result ">
                                                        <option value="">請選擇審核結果</option>
                                                        <option value=" 1 " >黑名單
                                                        </option>
                                                        <option value=" 2 " >待核驗
                                                        </option>
                                                        <option value=" 3 " >RCA
                                                        </option>
                                                        <option value=" 4 " >PEP
                                                        </option>
                                                        <option value=" 5 " >負面新聞
                                                        </option>
                                                        <option value=" 6 " >No record
                                                        </option>
                                                        <option value=" 7 " >無法核驗結果
                                                        </option>
                                                        <option value=" 8 " >告誡名單
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                            <input type=" hidden " name=" module " value=" laundering ">
                                            <input type=" hidden " name=" action " value=" index ">
                                            <input type=" hidden " name=" risk " value=" 1 ">
                                            <input type=" hidden " name=" check_status " value=" 1 ">
                                            <div class=" layui-input-inline ">
                                                <div class=" layui-btn-group ">
                                                    <button type=" submit " class=" layui-btn " style=" padding-top: 0 ">
                                                        <i class=" layui-icon layui-icon-search "></i>
                                                    </button>
                                                    <button id=" reset " type=" reset " class=" layui-btn layui-btn-primary " style=" padding-top: 0 ">
                                                        重置
                                                    </button>
                                                    <button id=" add " type=" button " data-type=" add " class=" layui-btn layui-btn-primary " style=" padding-top: 0 ">
                                                        新增用户
                                                    </button>
                                                                                                        <button id=" check_all " type=" button " ids=" 0 " class=" layui-btn layui-btn-primary layui-border-red " style=" padding-top: 0 ">
                                                        批量複核
                                                    </button>
                                                                                                    </div>
                                            </div>
                                            <div class=" layui-input-inline " style=" margin-left: 20px;">
                                                &nbsp;&nbsp;&nbsp;註：24:00-7:00為政府審核網站維護期，不進行送審
                                            </div>
                                        </div>
                                    </form>
                                    <br/>
                                    <div class=" layui-form ">
                                        <table class=" layui-table " lay-size=" sm ">
                                            <thead>
                                            <tr>
                                                                                                <th><input type=" checkbox " name="" lay-skin=" primary " lay-filter=" allChoose "></th>
                                                                                                <th>會員編號</th>
                                                <th>送審姓名</th>
                                                <th>送審開始時間</th>
                                                <th>審核完成時間</th>
                                                <th>審核結果</th>
                                                <th>部門主管</th>
                                                <th>高階管理人員 </th>
                                                <th>會員資金來源</th>
                                                <th style=" word-break: keep-all;width: 10%">操作</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                                                                                                                <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526983 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=915594 " class='hide-val' real='915594' hide='9****4' target=" _blank ">9****4</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳宥任' hide='陳*任'>陳*任</td>
                                                        <td>2025-07-31 16:29:58</td>
                                                        <td>2025-07-31 16:30:00</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-08-01 00:10:36                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-08-01 00:24:19                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526983 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526983 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526983 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526940 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=2878341 " class='hide-val' real='2878341' hide='2****41' target=" _blank ">2****41</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='黃俊傑' hide='黃*傑'>黃*傑</td>
                                                        <td>2025-07-31 13:25:17</td>
                                                        <td>2025-07-31 13:58:49</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 13:30:08                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 13:51:57                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526940 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526940 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526940 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526917 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1951081 " class='hide-val' real='1951081' hide='1****81' target=" _blank ">1****81</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='林俊宏' hide='林*宏'>林*宏</td>
                                                        <td>2025-07-31 11:34:15</td>
                                                        <td>2025-07-31 11:34:18</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526917 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526917 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526917 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526917 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526917 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526917 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526917 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526895 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4204007 " class='hide-val' real='4204007' hide='4****07' target=" _blank ">4****07</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳威全' hide='陳*全'>陳*全</td>
                                                        <td>2025-07-31 10:49:17</td>
                                                        <td>2025-07-31 10:49:20</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 20:08:55                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 20:33:33                                                                                                                    </td>
                                                        <td>存款</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526895 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526895 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526895 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526891 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=875976 " class='hide-val' real='875976' hide='8****6' target=" _blank ">8****6</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='黃子瑋' hide='黃*瑋'>黃*瑋</td>
                                                        <td>2025-07-31 09:45:21</td>
                                                        <td>2025-07-31 10:40:40</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 09:49:08                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526891 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526891 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526891 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526891 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526891 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526876 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1019010 " class='hide-val' real='1019010' hide='1****10' target=" _blank ">1****10</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳思齊' hide='陳*齊'>陳*齊</td>
                                                        <td>2025-07-31 06:48:09</td>
                                                        <td>2025-07-31 10:58:43</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 06:52:10                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 07:06:44                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526876 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526876 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526876 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526848 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3433078 " class='hide-val' real='3433078' hide='3****78' target=" _blank ">3****78</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='王耀毅' hide='王*毅'>王*毅</td>
                                                        <td>2025-07-31 10:54:06</td>
                                                        <td>2025-07-31 10:54:08</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526848 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526848 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526848 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526848 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526848 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526848 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526848 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526847 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3739444 " class='hide-val' real='3739444' hide='3****44' target=" _blank ">3****44</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張上楷' hide='張*楷'>張*楷</td>
                                                        <td>2025-07-31 10:53:47</td>
                                                        <td>2025-07-31 10:53:49</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526847 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526847 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526847 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526847 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526847 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526847 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526847 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526846 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3973489 " class='hide-val' real='3973489' hide='3****89' target=" _blank ">3****89</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='王陳右' hide='王*右'>王*右</td>
                                                        <td>2025-07-31 10:53:42</td>
                                                        <td>2025-07-31 10:53:47</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526846 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526846 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526846 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526846 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526846 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526846 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526846 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526845 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3832585 " class='hide-val' real='3832585' hide='3****85' target=" _blank ">3****85</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳文漢' hide='陳*漢'>陳*漢</td>
                                                        <td>2025-07-31 10:53:41</td>
                                                        <td>2025-07-31 10:53:42</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526845 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526845 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526845 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526845 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526845 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526845 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526845 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526843 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=2706550 " class='hide-val' real='2706550' hide='2****50' target=" _blank ">2****50</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='何應辰' hide='何*辰'>何*辰</td>
                                                        <td>2025-07-31 10:53:38</td>
                                                        <td>2025-07-31 10:53:41</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526843 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526843 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526843 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526843 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526843 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526843 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526843 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526842 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3231815 " class='hide-val' real='3231815' hide='3****15' target=" _blank ">3****15</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張湘敏' hide='張*敏'>張*敏</td>
                                                        <td>2025-07-31 10:53:36</td>
                                                        <td>2025-07-31 10:53:38</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526842 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526842 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526842 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526842 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526842 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526842 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526842 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526841 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1138256 " class='hide-val' real='1138256' hide='1****56' target=" _blank ">1****56</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='李彥龍' hide='李*龍'>李*龍</td>
                                                        <td>2025-07-31 10:53:33</td>
                                                        <td>2025-07-31 10:53:36</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526841 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526841 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526841 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526841 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526841 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526841 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526841 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526840 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1953366 " class='hide-val' real='1953366' hide='1****66' target=" _blank ">1****66</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='李仕祺' hide='李*祺'>李*祺</td>
                                                        <td>2025-07-31 10:53:31</td>
                                                        <td>2025-07-31 10:53:33</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526840 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526840 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526840 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526840 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526840 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526840 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526840 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526839 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3520461 " class='hide-val' real='3520461' hide='3****61' target=" _blank ">3****61</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='葉宸安' hide='葉*安'>葉*安</td>
                                                        <td>2025-07-31 10:53:29</td>
                                                        <td>2025-07-31 10:53:31</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-08-01 00:11:36                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-08-01 00:13:45                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526839 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526839 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526839 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526838 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=2774968 " class='hide-val' real='2774968' hide='2****68' target=" _blank ">2****68</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張富威' hide='張*威'>張*威</td>
                                                        <td>2025-07-31 10:53:22</td>
                                                        <td>2025-07-31 10:53:27</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526838 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526838 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526838 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526838 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526838 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526838 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526838 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526837 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1686444 " class='hide-val' real='1686444' hide='1****44' target=" _blank ">1****44</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='賴志旻' hide='賴*旻'>賴*旻</td>
                                                        <td>2025-07-31 10:53:20</td>
                                                        <td>2025-07-31 10:53:22</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526837 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526837 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526837 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526837 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526837 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526837 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526837 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526827 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3707493 " class='hide-val' real='3707493' hide='3****93' target=" _blank ">3****93</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='黃國銘' hide='黃*銘'>黃*銘</td>
                                                        <td>2025-07-31 10:52:39</td>
                                                        <td>2025-07-31 10:52:44</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526827 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526827 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526827 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526827 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526827 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526827 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526827 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526806 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4218545 " class='hide-val' real='4218545' hide='4****45' target=" _blank ">4****45</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='胡景彬' hide='胡*彬'>胡*彬</td>
                                                        <td>2025-07-31 10:52:25</td>
                                                        <td>2025-07-31 10:52:27</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-08-01 00:51:49                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-08-01 01:18:40                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526806 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526806 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526806 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526797 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3060763 " class='hide-val' real='3060763' hide='3****63' target=" _blank ">3****63</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='林敬恩' hide='林*恩'>林*恩</td>
                                                        <td>2025-07-31 10:47:13</td>
                                                        <td>2025-07-31 10:47:16</td>
                                                        <td>
                                                                                                                        RCA                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526797 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526797 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526797 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526797 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526797 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526797 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526797 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526796 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4215959 " class='hide-val' real='4215959' hide='4****59' target=" _blank ">4****59</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳國華' hide='陳*華'>陳*華</td>
                                                        <td>2025-07-31 10:44:50</td>
                                                        <td>2025-07-31 10:44:52</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526796 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526796 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526796 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526796 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526796 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526796 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526796 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526782 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4219452 " class='hide-val' real='4219452' hide='4****52' target=" _blank ">4****52</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='李羽婷' hide='李*婷'>李*婷</td>
                                                        <td>2025-07-31 10:41:40</td>
                                                        <td>2025-07-31 10:41:42</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526782 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526782 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526782 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526782 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526782 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526782 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526782 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526769 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=2650943 " class='hide-val' real='2650943' hide='2****43' target=" _blank ">2****43</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='謝博隆' hide='謝*隆'>謝*隆</td>
                                                        <td>2025-07-31 10:44:42</td>
                                                        <td>2025-07-31 10:44:44</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 17:54:01                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 17:56:56                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526769 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526769 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526769 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526760 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=107447 " class='hide-val' real='107447' hide='1****7' target=" _blank ">1****7</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='簡嘉佑' hide='簡*佑'>簡*佑</td>
                                                        <td>2025-07-30 19:51:56</td>
                                                        <td>2025-07-30 19:51:59</td>
                                                        <td>
                                                                                                                        PEP                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-30 19:58:04                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-30 20:03:04                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526760 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526760 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526760 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526755 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4219601 " class='hide-val' real='4219601' hide='4****01' target=" _blank ">4****01</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='劉家瑋' hide='劉*瑋'>劉*瑋</td>
                                                        <td>2025-07-31 10:40:34</td>
                                                        <td>2025-07-31 10:40:36</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526755 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526755 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526755 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526755 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>存款</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526755 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526755 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526755 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526750 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3897571 " class='hide-val' real='3897571' hide='3****71' target=" _blank ">3****71</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='林宥廷' hide='林*廷'>林*廷</td>
                                                        <td>2025-07-31 10:43:54</td>
                                                        <td>2025-07-31 10:43:59</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 22:45:47                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 23:01:13                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526750 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526750 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526750 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526734 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4121317 " class='hide-val' real='4121317' hide='4****17' target=" _blank ">4****17</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='徐芝恩' hide='徐*恩'>徐*恩</td>
                                                        <td>2025-07-31 11:35:36</td>
                                                        <td>2025-07-31 11:35:38</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526734 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526734 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526734 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526734 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526734 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526734 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526734 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526713 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=472970 " class='hide-val' real='472970' hide='4****0' target=" _blank ">4****0</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='黃國信' hide='黃*信'>黃*信</td>
                                                        <td>2025-07-31 10:38:16</td>
                                                        <td>2025-07-31 10:38:17</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526713 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526713 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526713 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526713 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526713 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526713 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526713 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526711 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4219529 " class='hide-val' real='4219529' hide='4****29' target=" _blank ">4****29</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張哲瑋' hide='張*瑋'>張*瑋</td>
                                                        <td>2025-07-31 11:35:30</td>
                                                        <td>2025-07-31 11:35:34</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526711 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526711 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526711 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526711 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526711 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526711 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526711 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526695 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=451890 " class='hide-val' real='451890' hide='4****0' target=" _blank ">4****0</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳冠霖' hide='陳*霖'>陳*霖</td>
                                                        <td>2025-07-30 19:49:09</td>
                                                        <td>2025-07-30 19:49:11</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 12:42:52                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 13:08:14                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526695 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526695 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526695 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526694 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3819861 " class='hide-val' real='3819861' hide='3****61' target=" _blank ">3****61</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='蔡尚勳' hide='蔡*勳'>蔡*勳</td>
                                                        <td>2025-07-31 10:36:19</td>
                                                        <td>2025-07-31 10:36:21</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526694 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526694 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526694 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526694 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526694 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526694 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526694 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526693 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=2661638 " class='hide-val' real='2661638' hide='2****38' target=" _blank ">2****38</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張晉維' hide='張*維'>張*維</td>
                                                        <td>2025-07-31 10:36:17</td>
                                                        <td>2025-07-31 10:36:19</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526693 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526693 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526693 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526693 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>投資收益轉入</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526693 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526693 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526693 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526637 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=772375 " class='hide-val' real='772375' hide='7****5' target=" _blank ">7****5</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='林麗玲' hide='林*玲'>林*玲</td>
                                                        <td>2025-07-31 10:46:36</td>
                                                        <td>2025-07-31 10:46:39</td>
                                                        <td>
                                                                                                                        PEP                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526637 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526637 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526637 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526637 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526637 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526637 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526637 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526587 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4219233 " class='hide-val' real='4219233' hide='4****33' target=" _blank ">4****33</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='曾俊偉' hide='曾*偉'>曾*偉</td>
                                                        <td>2025-07-31 10:46:08</td>
                                                        <td>2025-07-31 10:46:10</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526587 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526587 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526587 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526587 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526587 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526587 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526587 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526584 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4219227 " class='hide-val' real='4219227' hide='4****27' target=" _blank ">4****27</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='李冠緯' hide='李*緯'>李*緯</td>
                                                        <td>2025-07-31 10:45:55</td>
                                                        <td>2025-07-31 10:45:57</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526584 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526584 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526584 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526584 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526584 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526584 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526584 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526582 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3713300 " class='hide-val' real='3713300' hide='3****00' target=" _blank ">3****00</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳柏均' hide='陳*均'>陳*均</td>
                                                        <td>2025-07-31 10:45:44</td>
                                                        <td>2025-07-31 10:45:53</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526582 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526582 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526582 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526582 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526582 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526582 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526582 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526563 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4184623 " class='hide-val' real='4184623' hide='4****23' target=" _blank ">4****23</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張鈞富' hide='張*富'>張*富</td>
                                                        <td>2025-07-30 19:59:59</td>
                                                        <td>2025-07-30 20:00:02</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526563 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526563 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526563 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526563 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526563 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526563 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526563 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526562 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1035681 " class='hide-val' real='1035681' hide='1****81' target=" _blank ">1****81</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='吳憲岳' hide='吳*岳'>吳*岳</td>
                                                        <td>2025-07-30 19:59:33</td>
                                                        <td>2025-07-30 19:59:36</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526562 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526562 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526562 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526562 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526562 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526562 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526562 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526561 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1861698 " class='hide-val' real='1861698' hide='1****98' target=" _blank ">1****98</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='詹凱亦' hide='詹*亦'>詹*亦</td>
                                                        <td>2025-07-30 19:59:28</td>
                                                        <td>2025-07-30 19:59:33</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526561 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526561 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526561 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526561 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526561 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526561 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526561 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526560 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3966347 " class='hide-val' real='3966347' hide='3****47' target=" _blank ">3****47</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張玉芬' hide='張*芬'>張*芬</td>
                                                        <td>2025-07-31 10:37:15</td>
                                                        <td>2025-07-31 10:37:17</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526560 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526560 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526560 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526560 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526560 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526560 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526560 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526559 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3981917 " class='hide-val' real='3981917' hide='3****17' target=" _blank ">3****17</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='洪贈淵' hide='洪*淵'>洪*淵</td>
                                                        <td>2025-07-30 19:57:16</td>
                                                        <td>2025-07-30 19:58:15</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526559 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526559 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526559 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526559 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526559 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526559 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526559 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526557 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4062575 " class='hide-val' real='4062575' hide='4****75' target=" _blank ">4****75</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='謝文義' hide='謝*義'>謝*義</td>
                                                        <td>2025-07-31 10:37:12</td>
                                                        <td>2025-07-31 10:37:15</td>
                                                        <td>
                                                                                                                        告誡名單
                                                            <br/>
                                                                                                                        No record                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526557 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526557 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526557 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526557 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526557 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526557 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526557 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526538 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=459985 " class='hide-val' real='459985' hide='4****5' target=" _blank ">4****5</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳柏豪' hide='陳*豪'>陳*豪</td>
                                                        <td>2025-07-30 19:50:18</td>
                                                        <td>2025-07-30 19:50:22</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-30 20:33:20                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-30 20:46:41                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526538 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526538 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526538 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526533 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=2968895 " class='hide-val' real='2968895' hide='2****95' target=" _blank ">2****95</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳品全' hide='陳*全'>陳*全</td>
                                                        <td>2025-07-30 19:50:16</td>
                                                        <td>2025-07-30 19:50:18</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-31 15:54:50                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-31 16:15:16                                                                                                                    </td>
                                                        <td>每月薪資所得</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526533 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526533 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526533 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526526 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=3278407 " class='hide-val' real='3278407' hide='3****07' target=" _blank ">3****07</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳昱廷' hide='陳*廷'>陳*廷</td>
                                                        <td>2025-07-29 22:31:08</td>
                                                        <td>2025-07-31 10:50:21</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-29 22:35:33                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-29 22:41:41                                                                                                                    </td>
                                                        <td>存款</td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526526 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526526 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526526 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526508 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4218812 " class='hide-val' real='4218812' hide='4****12' target=" _blank ">4****12</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='陳大衛' hide='陳*衛'>陳*衛</td>
                                                        <td>2025-07-30 19:49:34</td>
                                                        <td>2025-07-30 19:49:36</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526508 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526508 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526508 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526508 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526508 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526508 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526508 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526494 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=151768 " class='hide-val' real='151768' hide='1****8' target=" _blank ">1****8</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='楊志文' hide='楊*文'>楊*文</td>
                                                        <td>2025-07-30 19:48:27</td>
                                                        <td>2025-07-30 19:48:35</td>
                                                        <td>
                                                                                                                        RCA                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526494 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526494 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526494 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526494 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526494 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526494 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526494 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526485 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1806984 " class='hide-val' real='1806984' hide='1****84' target=" _blank ">1****84</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='張家瑋' hide='張*瑋'>張*瑋</td>
                                                        <td>2025-07-30 19:47:41</td>
                                                        <td>2025-07-30 19:47:43</td>
                                                        <td>
                                                                                                                        負面新聞                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-30 19:56:44                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-30 20:06:03                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526485 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526485 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526485 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526484 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=4219066 " class='hide-val' real='4219066' hide='4****66' target=" _blank ">4****66</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='黃文祥' hide='黃*祥'>黃*祥</td>
                                                        <td>2025-07-30 19:47:38</td>
                                                        <td>2025-07-30 19:47:41</td>
                                                        <td>
                                                                                                                        PEP                                                        </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526484 " data-type=" 1 " data-time-field=" agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit "  data-id=" 526484 " data-type=" 2 " data-time-field=" agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td>
                                                                                                                            <button class=" layui-btn layui-btn-xs check-audit " data-id=" 526484 " data-type=" 1 " data-time-field=" manager_agreed_at ">通過</button>
                                                                <button class=" layui-btn layui-btn-xs layui-btn-primary check-audit " data-id=" 526484 " data-type=" 2 " data-time-field=" manager_agreed_at ">拒絕</button>
                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526484 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526484 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526484 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                    <tr>
                                                                                                                    <td><input type=" checkbox " name=" id_array[]"  value=" 526463 "  lay-skin=" primary " lay-filter=" itemChoose " ></td>
                                                                                                                <td class=" show-hide ">
                                                            <a href=" ./admin.php?module=userInfo&action= list&id=1013757 " class='hide-val' real='1013757' hide='1****57' target=" _blank ">1****57</a></td>
                                                        <td  class=" show-hide "><span class='hide-val' real='黃文龍' hide='黃*龍'>黃*龍</td>
                                                        <td>2025-07-30 19:46:14</td>
                                                        <td>2025-07-30 19:46:16</td>
                                                        <td>
                                                                                                                        RCA                                                        </td>
                                                        <td>
                                                                                                                             鐘駿 已通過<br/>
                                                                2025-07-30 19:51:15                                                                                                                    </td>
                                                        <td>
                                                                                                                             張巧妮 已通過<br/>
                                                                2025-07-30 19:58:31                                                                                                                    </td>
                                                        <td></td>
                                                        <td>
                                                            <div class=" layui-btn-group ">
                                                                                                                                                                                                                                                                    <a class=" layui-btn layui-btn-xs check_again " data-id=" 526463 ">復核通过</a>
                                                                                                                                                                                                    <br/><a class=" layui-btn layui-btn-xs layui-btn-primary " data-type=" modify " data-title=" 輸入客服備註 " data-id=" 526463 ">修改</a>
                                                                                                                                <br/><a class=" layui-btn layui-btn-xs layui-btn-primary review-record " data-id=" 526463 ">送審記錄</a>
                                                            </div>
                                                                                                                    </td>
                                                    </tr>
                                                                                                                                        </tbody>
                                        </table>
                                        <div class=" layui-box layui-laypage layui-laypage-default " id=" layui-laypage-1 " style=" width: 100%;font-size: 12px ">
                                            <form name=" sf " method=" get " action=" ?" onsubmit=" document.sf.firstRow.value=(document.sf.firstRow.value-1)*50 "><div style=" float:left;width:85%">第<b>1</b>/611頁&nbsp;&nbsp;&nbsp;&nbsp;  <span class=" pageCurrentfang ">1</span> <a class=" pageNum " href='admin.php?firstRow=50&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>2</a>  <a class=" pageNum " href='admin.php?firstRow=100&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>3</a>  <a class=" pageNum " href='admin.php?firstRow=150&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>4</a>  <a class=" pageNum " href='admin.php?firstRow=200&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>5</a>  <a class=" pageNum " href='admin.php?firstRow=250&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>6</a>  <a class=" pageNum " href='admin.php?firstRow=300&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>7</a>  <a class=" pageNum " href='admin.php?firstRow=350&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>8</a>  <a class=" pageNum " href='admin.php?firstRow=400&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>9</a>  <a class=" pageNum " href='admin.php?firstRow=450&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'>10</a>  <span class=" pageNum ">...</span> <input type=" hidden " name=" totalRows " value=" 30502 " /><input type=" hidden " name=" module " value=" laundering " /><input type=" hidden " name=" action " value=" index " /><input type=" hidden " name=" risk " value=" 1 " /><input type=" hidden " name=" check_status " value=" 1 " /><input type=" text " name=" firstRow " style=" width:30px;" /> </form>  <a href='admin.php?firstRow=50&totalRows=30502&module=laundering&action=index&risk=1&check_status=1'><img width=" 54 " height=" 20 " border=" 0 " align=" absmiddle " src=" //image.8591.com.tw/index/public/global/next1.gif " alt=" 下一頁 " /></a> &nbsp;&nbsp;共 <span class=" R ">30502</span> 筆</div>                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<div id=" Foot ">
  <hr>
  Copyright &copy; 2004-2025 www.8591.com.tw
</div>
<style>
#scrollBar {
  overflow: hidden;
  position: fixed;
  bottom: 10px;;
  right: 10px;;
  width: 47px;
  cursor: pointer;
  z-index: 501;
}
#scrollBar a{
  width: 31px;
  height: 37px;
  padding:8px 8px 0;
  display: block;
  margin-top: 5px;
  border-right: 9px;
  background-color:#878787;
  color:#fff;
  text-decoration:none;
  -webkit-transition: all 0.18s ease-out;
  -moz-transition: all 0.18s ease-out;
  -ms-transition: all 0.18s ease-out;
  -o-transition: all 0.18s ease-out;
  transition: all 0.18s ease-out;
}
#scrollBar a:hover{
  background:#ff6600;
}
</style>
<div id=" pop_sbSetTitle " style=" display:none;"><div><input type=" text " id=" pop_setTitle " /><input type=" button " value=" 修改 " id=" pop_setTitle_btn " /></div></div>
<div id='person_info'>
    <input type=" hidden " name=" admin_user_id " value=" 354 ">
    <input type=" hidden " name=" admin_user_name " value=" 黃興興 ">
    <input type=" hidden " name=" user_id " value="">
    <input type=" hidden " name=" controller " value=" laundering ">
    <input type=" hidden " name=" method " value=" index ">
</div>

<script>
/**************** 回到頂部 Start *********************/
$(window).bind(" scroll ", function(){
  if($(window).scrollTop() == 0){
    $('.sb_goTop').hide();
  }else{
    $('.sb_goTop').css('display','block');
  }
});

$(window).keydown(function (e) {
    // console.log('foot tpl keydown:'+e.which)
    if(e.which == 27){
        layer.closeAll();
    }
});

$('.sb_goTop').click(function(){
  $(" html, body ").animate({ scrollTop: 0 }, 120)
});
/**************** 回到頂部 End *********************/
/**************** 修改標題 Start *********************/
var pop_sbSetTitle = '';

$('.sb_setTitle').click(function(){

  if(pop_sbSetTitle == ''){
    pop_sbSetTitle = $('#pop_sbSetTitle').html();
    $('#pop_sbSetTitle').remove();
  }
  $.popbox({
    id:" jbox_popbox_html ",
    title:" 修改標題 ",
    style: " popboxNew ",
    type:" html ",
    content: pop_sbSetTitle,
    size:{" width":" 300px"," height":" 80px "},
    drag:true,
    shade:true,
    closeback:tag = 0
  });
  $(" html, body ").animate({ scrollTop: 0 }, 120);
  $('#jbox_popbox_html').css('top',0);
});

$('body').on('click', '#pop_setTitle_btn', function() {
  document.title = $('#pop_setTitle').val()
  $.popbox.close('jbox_popbox_html')
})

// $('#pop_setTitle_btn').live('click',function(){
//   document.title = $('#pop_setTitle').val();
//   $.popbox.close('jbox_popbox_html');
// })

/**************** 修改標題 End *********************/
/**************** 鎖定頁面 Start *********************/
$(function(){
    var timer = null;

    // 保持登入
    function keeplive(keeplive_on){
        var keeplive_str = keeplive_on ? '保持登入' : '取消保持' ;

        console.log(keeplive_str);

        if(keeplive_on==0){
            window.clearInterval(timer);
            return;
        }

        timer = window.setInterval(function(){
            var i = new Image().src = '/admin.php?action=keeplive&admin_id='+354;
        }, 30000);
        return;
    }

    // 自動保持登入
    keeplive($('#keeplive-checkbox').is(':checked'));

    $('#keeplive-checkbox').change(function() {
        var keep = $(this).is(':checked') ? 1 : 0;

        keeplive(keep);

        $.cookie('KEEPLIVE_ON', keep);
    });

    //個資隱藏部分
    var trigger = null;
    var complete = 1;
    $('.show-hide').hover(function(){
        var $this  = $(this);
        var content = '';
        $this.find('.hide-val').each(function(){
          var real = $(this).attr('real');
          content += ','+real;
          if (real) {
            $(this).html(real.replace(/\{#\{(.+?)\}#\}/gs, "<span style='color:red'>$1</span>"));
          }
        });
        if($(this).find('.block-show').html()!= null ){
            content += ','+$(this).find('.block-show').html();
        }
        content = content.slice(1);
        trigger = setTimeout(function(){
            //alert(trigger);
            if(complete === 1){
                recordPersonInfo(content);
            }
        },1000);
        //console.log(content);
        $(this).find('.block-hide').hide();
        $(this).find('.block-show').show();
    },function(){
        var $this  = $(this);
        $this.find('.hide-val').each(function(){
            var hide = $(this).attr('hide');
            $(this).html(hide);
        });
        $(this).find('.block-hide').show();
        $(this).find('.block-show').hide();

        clearTimeout(trigger);
    });
    //個資隱藏部分 大塊
    $('.hide-val-block').hover(function(){
        var content = '';
        if($(this).find('.block-show').html()!= null ){
            content += $(this).find('.block-show').html();
        }
        trigger = setTimeout(function(){
            if(complete === 1){
                 recordPersonInfo(content);
            }
        },1000);
        //console.log(content);
        $(this).find('.block-hide').hide();
        $(this).find('.block-show').show();
    },function(){
        $(this).find('.block-hide').show();
        $(this).find('.block-show').hide();
        clearTimeout(trigger);
    });

    window.recordPersonInfo = function  (content){
        var data = {};
        var $           = jQuery;
        data['module'] = 'userInfo';
        data['action'] = 'recordPersonInfo';
        data['admin_user_id'] = $("div#person_info input[name='admin_user_id']").val();;
        data['admin_user_name'] = $("div#person_info input[name='admin_user_name']").val();
        data['user_id'] = $("div#person_info input[name='user_id']").val();
        data['controller'] = $("div#person_info input[name='controller']").val();
        data['method'] = $("div#person_info input[name='method']").val();
        data['content'] = content;
        if(data['controller'] == 'userInfo' &&data['method'] == 'personInfoList'){
            return false;
        }
        $.ajax({
          type: 'POST',
          url:'ajax.php',
          data:data,
          dataType:'json',

          beforeSend:function(){//觸發ajax請求開始時執行
             complete = 0;
          },
          success:function(json) {
            if(json.status == 1){
                console.log(json.info);
            }else{
                console.log(json.info);
            }
          },
          error:function(){//ajax發生錯誤時執行
               console.log('數據請求出錯');
          },
          complete:function(){//ajax請求完成時執行
              complete = 1;
          }
        });
    }
});
/**************** 鎖定頁面 End *********************/
$.fn.check = function(mode){
   var mode = mode || 'on'; //default
    return this.each(function(){
        switch(mode){
            case 'on':
                this.checked = true;
                break;
            case 'off':
                this.checked = false;
                break;
            case 'toggle':
              this.checked = !this.checked;
                break;
        }
    });
}

// 首頁頂部輪播
if($("#notice-slider li").length > 1){
  setInterval(function(){
    $("#notice-slider li").eq(0).animate({'margin-top': '-26px'}, 1000,function(){
      $(this).css('margin','0').clone().appendTo($("#notice-slider"));
      $(this).remove();
    });
  },5000);
}



</script><script>
    // 參數
    var DEPARTMENT = 'tw8591';
    var TOKEN = 'ZcTTpsjhyYIcCaI2i3US';
    var STAFF_ID = "10175";
    var STAFF_MOBILE = "13570881861";
    // 员工手机号
    var DIALBACK = '0';
    // 0 = 座机 1 = 手机
    var DIALBACKUSER = '10069,';
    DIALBACKUSER = DIALBACKUSER.split(',')

    console.log(DEPARTMENT, TOKEN, STAFF_ID, STAFF_MOBILE, 'DIALBACK:', DIALBACK, 'DIALBACKUSER:', DIALBACKUSER);

    // 來電彈屏
    try {
        // 檢測用户授權
        Notification.requestPermission(function(permission) {
            if ("granted" != permission) {
                console.log('用户未授權');
                return;
            }

            $.getScript('https://kf.addcn.com/js/callscreen.js?2018092101', function() {
                if (typeof CallScreen == 'undefined') {
                    console.log('接入來電提醒功能異常');
                    return;
                }

                // 來電彈屏
                // CallScreen.testCtrlWin({"popwin":"open", "callerid":"43124324"})

                if (!STAFF_ID) {
                    console.log('未設置工號，請前往 https://admin.8591.com.tw/admin.php?module=profile&action=edit 設置');
                    return;
                }

                CallScreen.init({
                    token: TOKEN,
                    // token值
                    dept: DEPARTMENT,
                    // 部門
                    ext: STAFF_ID,
                    // 分機號碼，默認為工號
                    callback_handler: function(detail) {
                        var mobile = detail.callerid || '';
                        if (!mobile) {
                            console.log('mobile is empty.');
                            return;
                        }

                        var title = '來電信息';
                        var body = '號碼：' + mobile;
                        var url = '/admin.php?module=userInfo&action=list&mobile=' + mobile;

                        var n = new Notification(title,{
                            requireInteraction: true,
                            body: body,
                            data: {
                                url: url,
                                time: 30000
                            },
                            tag: 'callscreen-' + mobile,
                            icon: 'http://s.8591.com.tw/img/admin/call_1097681.png',
                        });

                        // 自動關閉
                        setTimeout(function() {
                            n.close();
                        }, n.data.time);

                        // 打開鏈接
                        if (url) {
                            n.onclick = function() {
                                window.open(n.data.url, '_blank');
                                this.close();
                            }
                        }
                    }
                });
            });
        });
    } catch (error) {
        console.log('瀏覽器不支持 Notification');
    }

    // 一鍵撥號
    // example:
    // 台灣：<i class="onedial" data-mobile="0912345678" data-name="張三">撥打台灣電話</i>
    // 香港：<i class="onedial" data-mobile="8888888" data-name="張三">撥打香港電話</i>
    $('.onedial').click(function() {
        var $this = $(this);
        var mobile = $this.attr('data-mobile');
        var name = $this.attr('data-name');

        if (DIALBACKUSER.includes(STAFF_ID) && !STAFF_MOBILE) {
            alert('請先設置手機號');
            return;
        }

        if (DIALBACK == 1 && !STAFF_MOBILE) {
            alert('請先設置手機號');
            return;
        }

        if (!mobile) {
            alert('缺省參數:mobile');
            return false;
        }

        // 兼容
        mobile = mobile.replace(/[\s\+]/g, '');

        // 香港电话
        if (/^\d{8}$/.test(mobile)) {
            mobile = '00852' + mobile;
        }

        var question = '回拨 ' + (name ? name : mobile) + ' 到 ' + (DIALBACK * 1 ? '手机' : '座机');
        if (DIALBACKUSER.includes(STAFF_ID)) {
            question = '回拨 ' + (name ? name : mobile) + ' 到 手机';
        }

        if (!confirm(question)) {
            return;
        }

        // 回拨
        var url = '';
        if (DIALBACKUSER.includes(STAFF_ID)) {
            DEPARTMENT = '8591';
            url = 'https://call.debug.591.com.tw/?call_id=' + STAFF_MOBILE + '&send_id=' + mobile + '&remote_id=' + DEPARTMENT + '&callback=?&api=v6';
        } else {
            switch (DIALBACK) {
            case '1':
                // 回拨手机
                DEPARTMENT = '8591';
                url = 'https://call.debug.591.com.tw/?call_id=' + STAFF_MOBILE + '&send_id=' + mobile + '&remote_id=' + DEPARTMENT + '&callback=?&api=v6'
                break;
            case '0':
                // 回拨座机
            default:
                url = 'https://kf.addcn.com/onedial/' + DEPARTMENT + '/' + STAFF_ID + '/' + mobile + '?callback=?';
                break;
            }
        }

        if (!url) {
            alert('_system.DIALBACK 配置錯誤');
            return;
        }

        console.log(url);

        $.ajax({
            type: 'get',
            url: url,
            dataType: 'jsonp',
            success: function(json) {
                if (!json.status) {
                    console.error(json.data);
                    return;
                }
                console.log('撥打成功');
            }
        });
    });
    //實名認證彈框
    function propIdcard(obj, uid) {
        console.log(obj.checked)
        console.log($("#addIdcard").prop("checked"));
        if (obj.checked) {
            layer.open({
                type: 2,
                offset: '30px',
                title: '新增證件',
                shadeClose: false,
                shade: false,
                maxmin: false,
                //开启最大化最小化按钮
                area: ['440px', '667px'],
                content: '/admin.php?module=idcard&action=idcard&from=userInfo&user_id=' + uid,
                cancel: function(index, layer) {
                    $('#addIdcard').prop('checked', false);
                }
            });
        }
    }
    // 增加人脸
    function propFace(obj, uid) {
        console.log(obj.checked)
        console.log($("#addFace").prop("checked"));
        layer.open({
            type: 2,
            offset: '0',
            title: "新增人脸认证",
            shade: 0,
            content: '/admin.php?module=recognition&action=add&from=userInfo&user_id=' + uid,
            area: ['600px', '600px'],
            btn: ['确认添加', '暫不操作'],
            btnAlign: 'c',
            yes: function(index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index]
                  , submitID = 'LAY-add-submit'
                  , submit = layero.find('iframe').contents().find('#' + submitID);
                //監聽提交
                iframeWindow.layui.form.on('submit(' + submitID + ')', function(data) {
                    var url = "./admin.php";
                    var field = data.field;
                    //獲取提交的字段
                    //提交 Ajax 成功後，靜態更新表格中的數據
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: field,
                        dataType: 'json',
                        success: function(data) {
                            console.log(data);
                            if (data.code == 200) {
                                layer.close(index);
                                //關閉彈層
                                layer.msg('认证成功', {
                                    offset: '200px'
                                }, function(index) {
                                    window.location.reload();
                                });
                            } else {
                                layer.msg(data.msg, {
                                    offset: '200px'
                                });
                            }
                        },
                        error: function() {
                            layer.msg('认证失敗', {
                                offset: '200px'
                            })
                        }
                    });
                });

                submit.trigger('click');
            },
            cancel: function(index, layer) {
                $('#addFace').prop('checked', false);
            }
        });
    }

    // 电话记录弹窗
    function propTell(obj, uid, fromTag) {
        var name = $(obj).attr('id');
        if (fromTag == "" || fromTag == undefined) {
            fromTag = $(obj).attr('item-tag');
        }
        // console.log(fromTag.indexOf('accountFocus,deal_appeal,deal_untransfer,deal_unaccept,deal_frozen,deal_accept_before,audit_id_card_reply') != -1);
        if ($(obj).prop('checked')) {
            if (uid == "" || uid == undefined) {
                uid = $(obj).attr('uid');
            }
            if (name == "addTellCall") {
                addTellCall(uid, fromTag, obj)
            } else {
                addTellReply(uid, fromTag, obj)
            }
        } else {
            $(obj).attr("tell_id", "");
            $(obj).val("checked");
            $(obj).removeAttr('checked');
            $("#addTellCallHide").val("");
            $("#addTellReplyHide").val("");
            $(obj).next('span').hide();
        }
    }
    function addTellCall(uid, fromTag, obj=null) {
        var height = '700px';
        if (fromTag == "" && fromTag == undefined) {
            fromTag = 99;
        }
        if (fromTag > 2) {
            height = '550px';
        }
        layer.open({
            type: 2,
            offset: '0',
            title: "新增来电",
            shade: 0,
            content: "./admin.php?module=serviceTelNew&action=tellCall&from=" + fromTag + "&uid=" + uid,
            area: ['900px', height],
            btn: ['確認添加', '暫不操作'],
            btnAlign: 'c',
            yes: function(index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index]
                  , submitID = 'LAY-add-submit'
                  , submit = layero.find('iframe').contents().find('#' + submitID);
                //監聽提交
                iframeWindow.layui.form.on('submit(' + submitID + ')', function(data) {
                    var url = "./admin.php";
                    var field = data.field;
                    //獲取提交的字段
                    //提交 Ajax 成功後，靜態更新表格中的數據
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: field,
                        dataType: 'json',
                        success: function(data) {
                            console.log(data);
                            if (data.status == 200) {
                                layer.close(index);
                                //關閉彈層
                                layer.msg('操作成功', {
                                    offset: '200px'
                                });
                                if (obj != null) {
                                    $(obj).attr("tell_id", data.id);
                                    $(obj).val(data.id);
                                    $("#addTellCallHide").val(data.id);
                                    $(obj).next('span').show();
                                }
                            } else {
                                layer.alert(data.msg ? data.msg : '操作失敗', {
                                    offset: '200px'
                                });
                            }
                        },
                        error: function() {
                            layer.alert('添加失敗', {
                                offset: '200px'
                            })
                        }
                    });
                });

                submit.trigger('click');
            }
        });
    }

    function addTellReply(uid, fromTag, obj=null) {

        console.log(fromTag);
        var offset = '0';
        var height = '600px';
        if (fromTag != "" && fromTag != undefined && fromTag > 2) {
            height = '450px';
        }
        var strTag = "accountFocus,id_card_reply,deal_appeal,deal_untransfer,deal_unaccept,deal_frozen,deal_accept_before,rename_reply,remobile_reply,take_money_change";
        if (fromTag != "" && fromTag != undefined && typeof fromTag == 'string' && strTag.indexOf(fromTag) != -1) {
            height = '240px';
        }
        console.log(height);
        layer.open({
            type: 2,
            offset: offset,
            title: "新增拨出",
            shade: 0,
            content: "./admin.php?module=serviceTelNew&action=tellReply&from=" + fromTag + "&uid=" + uid,
            area: ['900px', height],
            btn: ['確認添加', '暫不操作'],
            btnAlign: 'c',
            yes: function(index, layero) {
                var iframeWindow = window['layui-layer-iframe' + index]
                  , submitID = 'LAY-add-submit'
                  , submit = layero.find('iframe').contents().find('#' + submitID);
                //監聽提交
                iframeWindow.layui.form.on('submit(' + submitID + ')', function(data) {
                    var url = "./admin.php";
                    var field = data.field;
                    //獲取提交的字段
                    //提交 Ajax 成功後，靜態更新表格中的數據
                    $.ajax({
                        type: "POST",
                        url: url,
                        data: field,
                        dataType: 'json',
                        success: function(data) {
                            console.log(data);
                            if (data.status == 200) {
                                layer.close(index);
                                //關閉彈層
                                layer.msg('操作成功', {
                                    offset: '200px'
                                });
                                if (obj != null) {
                                    $(obj).attr("tell_id", data.id);
                                    $(obj).val(data.id);
                                    $("#addTellReplyHide").val(data.id);
                                    $(obj).next('span').show();
                                }
                            } else {
                                layer.alert('操作失敗', {
                                    offset: '200px'
                                });
                            }
                        },
                        error: function() {
                            layer.alert('添加失敗', {
                                offset: '200px'
                            })
                        }
                    });
                });

                submit.trigger('click');
            }
        });
    }
</script>
<script>
    var layer;
    $(document).ready(function() {
        layui.config({
            base: '//static.8591.com.tw/javascript/layui/lay/modules/',
        }).use(['jquery', 'layer', 'form', 'laydate', 'table'], function() {
            layer = layui.layer;
            var $ = layui.jquery
              , form = layui.form
              , laydate = layui.laydate
              , table = layui.table;
            //日期時間範圍
            laydate.render({
                elem: '#startDate',
                type: 'date',
                value: ""
            });

            laydate.render({
                elem: '#endDate',
                type: 'date',
                value: ""
            });

            //全选or 取消全选
            form.on('checkbox(allChoose)', function(data) {
                //所有
                var all_child = $(data.elem).parents('table').find('tbody input[type="checkbox"]');
                //data.elem.checked 值为 true 或者 false
                all_child.each(function(index, item) {
                    item.checked = data.elem.checked;
                });
                form.render('checkbox');

                //获取已选中
                var checked_child = $(data.elem).parents('table').find('tbody input[type="checkbox"]:checked');
                layer.msg('选中了：' + checked_child.length + ' 个');
                //获取选中的值
                var arr_box = [];
                checked_child.each(function() {
                    arr_box.push($(this).val());
                });
                console.log(arr_box.toString());
                $("#check_all").attr('ids', arr_box.toString())
            });

            //全选和部分选中时候,表头全选按钮的样式变化
            form.on('checkbox(itemChoose)', function(data) {
                var sib = $(data.elem).parents('table').find('tbody input[type="checkbox"]:checked').length;
                var total = $(data.elem).parents('table').find('tbody input[type="checkbox"]').length;
                if (sib == total) {
                    $(data.elem).parents('table').find('thead input[type="checkbox"]').prop("checked", true);
                } else {
                    $(data.elem).parents('table').find('thead input[type="checkbox"]').prop("checked", false);
                }
                form.render('checkbox');

                //获取已选中
                var child = $(data.elem).parents('table').find('tbody input[type="checkbox"]:checked');
                // layer.msg('选中了：'+ child.length + ' 个');
                //获取选中的值
                var arr_box = [];
                child.each(function() {
                    arr_box.push($(this).val());
                });
                console.log(arr_box.toString());
                $("#check_all").attr('ids', arr_box.toString())
            });

            // 批量复核
            $("#check_all").click(function() {
                var ids = $(this).attr('ids');
                var uri = "./admin.php?module=laundering&action=check&ids=" + ids;
                $.ajax({
                    type: 'GET',
                    url: uri,
                    dataType: 'json',
                    success: function(json) {
                        if (json.code == 400) {
                            layer.msg(json.msg, {
                                offset: '200px'
                            });
                            return false;
                        }
                        layer.msg('複核成功', {
                            offset: '200px'
                        }, function(index) {
                            window.location.reload();
                        });

                    }
                });
            });
            $(".check_again").click(function() {
                var id = $(this).data('id');
                var uri = "./admin.php?module=laundering&action=check&ids=" + id;
                $.ajax({
                    type: 'GET',
                    url: uri,
                    dataType: 'json',
                    success: function(json) {
                        if (json.code == 400) {
                            layer.msg(json.msg, {
                                offset: '200px'
                            });
                            return false;
                        }
                        layer.msg('複核成功', {
                            offset: '200px'
                        }, function(index) {
                            window.location.reload();
                        });

                    }
                });
            });

            $('.check-audit').on('click', function() {
                let id = $(this).data('id');
                let type = $(this).data('type');
                let time_field = $(this).data('time-field');
                let uri = "./admin.php";
                let that = $(this);
                $.ajax({
                    type: 'POST',
                    url: uri,
                    data: {
                        module: 'laundering',
                        action: 'checkAudit',
                        id,
                        type,
                        time_field,
                    },
                    dataType: 'json',
                    success: function(json) {
                        if (json.code !== 200) {
                            layer.msg(json.msg, {
                                offset: '200px'
                            });
                            return false;
                        }

                        that.parent().html((type == '1' ? '已通過' : '已拒絕') + '<br/>' + json.data.agreed_at);

                        layer.msg('審核成功', {
                            offset: '200px'
                        });

                    },
                    error: function() {
                        layer.msg('操作失敗', {
                            offset: '200px'
                        });
                    }
                });
            })

            $('.review-record').click(function() {
                let id = $(this).data('id');
                let uri = "./admin.php?module=laundering&action=reviewRecord&id=" + id;

                layer.open({
                    type: 2,
                    offset: '0',
                    title: '送審記錄',
                    shade: 0,
                    content: uri,
                    area: ['600px', '400px'],
                    btnAlign: 'c'
                });
            });

            //监听指定开关
            $(".op_check").click(function() {
                var uri = $(this).data('uri');
                $.ajax({
                    type: 'GET',
                    url: uri,
                    dataType: 'json',
                    success: function(json) {
                        if (json.code == 400) {
                            layer.msg(json.msg, {
                                offset: '200px'
                            });
                            return false;
                        }
                        layer.msg('切换成功', {
                            offset: '200px'
                        }, function(index) {
                            window.location.reload();
                        });

                    }
                });
            });

            var active = {
                confirm: function() {
                    var id = $(this).attr('data-id');
                    var title = $(this).attr('data-title');
                    layer.open({
                        type: 2,
                        offset: '0',
                        title: title,
                        shade: 0,
                        content: "./admin.php?module=laundering&action=antiConfirm&id=" + id,
                        area: ['700px', '500px'],
                        btn: ['确认修改', '暫不操作'],
                        btnAlign: 'c',
                        yes: function(index, layero) {
                            var iframeWindow = window['layui-layer-iframe' + index]
                              , submitID = 'LAY-add-submit'
                              , submit = layero.find('iframe').contents().find('#' + submitID);
                            //監聽提交
                            iframeWindow.layui.form.on('submit(' + submitID + ')', function(data) {
                                var url = "./admin.php";
                                var field = data.field;
                                //獲取提交的字段
                                //提交 Ajax 成功後，靜態更新表格中的數據
                                $.ajax({
                                    type: "POST",
                                    url: url,
                                    data: field,
                                    dataType: 'json',
                                    success: function(data) {
                                        console.log(data);
                                        if (data.code == 200) {
                                            layer.close(index);
                                            //關閉彈層
                                            layer.msg('編輯成功', {
                                                offset: '200px'
                                            }, function(index) {
                                                window.location.reload();
                                            });
                                        } else {
                                            layer.msg('操作失敗', {
                                                offset: '200px'
                                            });
                                        }
                                    },
                                    error: function() {
                                        layer.msg('添加失敗', {
                                            offset: '200px'
                                        })
                                    }
                                });
                            });

                            submit.trigger('click');
                        }
                    });
                },
                modify: function() {
                    var id = $(this).attr('data-id');
                    var title = $(this).attr('data-title');
                    layer.open({
                        type: 2,
                        offset: '0',
                        title: title,
                        shade: 0,
                        content: "./admin.php?module=laundering&action=modify&id=" + id,
                        area: ['600px', '400px'],
                        btn: ['确认修改', '暫不操作'],
                        btnAlign: 'c',
                        yes: function(index, layero) {
                            var iframeWindow = window['layui-layer-iframe' + index]
                              , submitID = 'LAY-add-submit'
                              , submit = layero.find('iframe').contents().find('#' + submitID);
                            //監聽提交
                            iframeWindow.layui.form.on('submit(' + submitID + ')', function(data) {
                                var url = "./admin.php";
                                var field = data.field;
                                //獲取提交的字段
                                //提交 Ajax 成功後，靜態更新表格中的數據
                                $.ajax({
                                    type: "POST",
                                    url: url,
                                    data: field,
                                    dataType: 'json',
                                    success: function(data) {
                                        console.log(data);
                                        if (data.code == 200) {
                                            layer.close(index);
                                            //關閉彈層
                                            layer.msg('編輯成功', {
                                                offset: '200px'
                                            }, function(index) {
                                                window.location.reload();
                                            });
                                        } else {
                                            layer.msg('操作失敗', {
                                                offset: '200px'
                                            });
                                        }
                                    },
                                    error: function() {
                                        layer.msg('添加失敗', {
                                            offset: '200px'
                                        })
                                    }
                                });
                            });

                            submit.trigger('click');
                        }
                    });
                },
                view: function(user_id) {
                    //页面层
                    layer.open({
                        title: "資金來源填寫記錄",
                        type: 2,
                        offset: '200px',
                        skin: 'layui-layer-rim',
                        //加上边框
                        area: ['850px', '540px'],
                        //宽高
                        content: "./admin.php?module=laundering&action=view&uid=" + user_id
                    });
                },
                add: function() {
                    layer.open({
                        type: 2,
                        offset: '0',
                        title: "新增送審用戶",
                        shade: 0,
                        content: "./admin.php?module=laundering&action=add",
                        area: ['600px', '300px'],
                        btn: ['确认添加', '暫不操作'],
                        btnAlign: 'c',
                        yes: function(index, layero) {
                            var iframeWindow = window['layui-layer-iframe' + index]
                              , submitID = 'LAY-add-submit'
                              , submit = layero.find('iframe').contents().find('#' + submitID);
                            //監聽提交
                            iframeWindow.layui.form.on('submit(' + submitID + ')', function(data) {
                                var url = "./admin.php";
                                var field = data.field;
                                //獲取提交的字段
                                //提交 Ajax 成功後，靜態更新表格中的數據
                                $.ajax({
                                    type: "POST",
                                    url: url,
                                    data: field,
                                    dataType: 'json',
                                    success: function(data) {
                                        console.log(data);
                                        if (data.code == 200) {
                                            layer.close(index);
                                            //關閉彈層
                                            layer.msg('編輯成功', {
                                                offset: '200px'
                                            }, function(index) {
                                                window.location.reload();
                                            });
                                        } else {
                                            layer.msg(data.msg, {
                                                offset: '200px'
                                            });
                                        }
                                    },
                                    error: function() {
                                        layer.msg('添加失敗', {
                                            offset: '200px'
                                        })
                                    }
                                });
                            });

                            submit.trigger('click');
                        }
                    });
                }
            };
            $('.layui-btn').on('click', function() {
                var type = $(this).attr('data-type');
                var id = $(this).attr('data-id');
                active[type] ? active[type].call(this, id) : '';
            });

            $("#reset").click(function() {
                window.location.href = "./admin.php?module=alipay&action=index";
            });
        })
    });
</script>
</body></html>
